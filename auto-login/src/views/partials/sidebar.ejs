<aside id="sidebar" class="bg-gray-800 text-white w-64 fixed inset-y-0 left-0 transform -translate-x-full lg:translate-x-0 transition-transform duration-300 ease-in-out z-30">
  <div class="h-full flex flex-col">
    <!-- Sidebar header - mobile only -->
    <div class="lg:hidden flex items-center justify-between p-4 border-b border-gray-700">
      <div class="flex items-center">
        <img src="https://kitsify.com/assets/images/logo_kitsify.png" alt="Kitsify Logo" class="h-8 w-auto mr-2">
        <span class="text-xl font-semibold">Kitsify Admin</span>
      </div>
      <button id="sidebar-close" class="text-gray-400 hover:text-white">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
    
    <!-- Sidebar content -->
    <div class="flex-1 overflow-y-auto py-4">
      <nav class="px-2 space-y-1">
        <!-- Dashboard -->
        <a href="/admin" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-gray-700 <%= currentPath === '/admin' ? 'bg-gray-900 text-white' : 'text-gray-300' %>">
          <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-6 w-6 <%= currentPath === '/admin' ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
          </svg>
          Dashboard
        </a>
        
        <!-- Account Management -->
        <a href="/admin/accounts" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-gray-700 <%= currentPath.startsWith('/admin/accounts') ? 'bg-gray-900 text-white' : 'text-gray-300' %>">
          <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-6 w-6 <%= currentPath.startsWith('/admin/accounts') ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          Accounts
        </a>
        
        <!-- Package Management -->
        <a href="/admin/packages" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-gray-700 <%= currentPath.startsWith('/admin/packages') ? 'bg-gray-900 text-white' : 'text-gray-300' %>">
          <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-6 w-6 <%= currentPath.startsWith('/admin/packages') ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
          </svg>
          Packages
        </a>
        
        <!-- Trial Users -->
        <a href="/admin/trial-users" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-gray-700 <%= currentPath.startsWith('/admin/trial-users') ? 'bg-gray-900 text-white' : 'text-gray-300' %>">
          <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-6 w-6 <%= currentPath.startsWith('/admin/trial-users') ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Trial Users
        </a>
        <!-- Cookies Management -->
        <a href="/admin/cookies" class="group flex items-center px-2 py-2 text-base font-medium rounded-md hover:bg-gray-700 <%= currentPath.startsWith('/admin/cookies') ? 'bg-gray-900 text-white' : 'text-gray-300' %>">
          <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-6 w-6 <%= currentPath.startsWith('/admin/cookies') ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300' %>" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Cookies
        </a>
      </nav>
    </div>
    
    <!-- Sidebar footer -->
    <div class="p-4 border-t border-gray-700">
      <div class="flex items-center">
        <div class="text-sm">
          <p class="text-gray-400">Kitsify Admin Panel</p>
          <p class="text-xs text-gray-500">© <%= new Date().getFullYear() %> Kitsify</p>
        </div>
      </div>
    </div>
  </div>
</aside>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Close sidebar on mobile when close button is clicked
    const sidebarClose = document.getElementById('sidebar-close');
    const sidebar = document.getElementById('sidebar');
    
    if (sidebarClose && sidebar) {
      sidebarClose.addEventListener('click', function() {
        sidebar.classList.add('-translate-x-full');
      });
    }
  });
</script>
