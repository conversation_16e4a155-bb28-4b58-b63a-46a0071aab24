import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreatePromptTranslationsTable1704000000000
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "prompt_translations" (
        "id" SERIAL NOT NULL,
        "prompt_id" integer NOT NULL,
        "lang" character varying(10) NOT NULL,
        "title" character varying(500) NOT NULL,
        "short_description" text,
        "optimization_guide" text,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_prompt_translations" PRIMARY KEY ("id"),
        CONSTRAINT "UQ_prompt_translations_prompt_id_lang" UNIQUE ("prompt_id", "lang"),
        CONSTRAINT "FK_prompt_translations_prompt_id" FOREIGN KEY ("prompt_id") REFERENCES "prompts"("id") ON DELETE CASCADE
      )
    `);

    // Create index for better query performance
    await queryRunner.query(`
      CREATE INDEX "IDX_prompt_translations_prompt_id_lang" ON "prompt_translations" ("prompt_id", "lang")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "prompt_translations"`);
  }
}
