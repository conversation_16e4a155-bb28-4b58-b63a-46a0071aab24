'use client';

import { useTranslations } from 'next-intl';
import { useEffect, useMemo, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import { type PromptVideoData, promptVideoService } from '@/services/prompt-video';

// TypeScript types
type Option = {
  value: string;
  label: string;
};

type Character = {
  id: number;
  description: string;
  appearance: string;
  emotion: string;
  mainAction: string;
  relatedObject: string;
  dialogue: string;
  imageRef: File | null;
  imageRefPreviewUrl: string;
};

type MultiSelectDropdownProps = {
  label: string;
  options: Option[];
  selectedValues: string[];
  onChange: (values: string[]) => void;
  placeholder: string;
};

type InputFieldProps = {
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder: string;
  children?: React.ReactNode;
  containerClassName?: string;
  labelClassName?: string;
  inputClassName?: string;
};

type ToastProps = {
  message: string;
  show: boolean;
};

// Reusable Component for multi-select dropdown fields
const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
  label,
  options = [],
  selectedValues,
  onChange,
  placeholder,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleOptionClick = (optionValue: string) => {
    const newSelectedValues = selectedValues.includes(optionValue)
      ? selectedValues.filter((value: string) => value !== optionValue)
      : [...selectedValues, optionValue];
    onChange(newSelectedValues);
  };

  // Extract the Vietnamese part for display
  const getDisplayValue = (value: string) => {
    // Find the matching option to get its label for display
    const option = options.find(opt => opt.value === value);
    if (option && option.label) {
      return option?.label?.split('(')[0]?.trim();
    }
    return value && typeof value === 'string' ? value.split('(')[0]?.trim() : '';
  };

  const displayString = selectedValues.length > 0
    ? selectedValues.map(getDisplayValue).join(', ')
    : placeholder;

  return (
    <div className="mb-4" ref={dropdownRef}>
      <label className="mb-2 block text-sm font-semibold text-gray-700">
        {label}
        :
      </label>
      <div className="relative">
        <button
          type="button"
          className="flex w-full items-center justify-between rounded-lg border border-gray-300 bg-white p-3 text-left text-gray-800 shadow-sm transition duration-200 ease-in-out focus:border-transparent focus:ring-2 focus:ring-blue-400"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="truncate">{displayString}</span>
          <svg className={`size-4 fill-current transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" /></svg>
        </button>
        {isOpen && (
          <div className="absolute z-10 mt-1 max-h-60 w-full overflow-y-auto rounded-lg border border-gray-300 bg-white shadow-lg">
            {options.map((option, index) => {
              const optionValue = option.value;
              const optionLabel = option.label;
              return (
                <div
                  key={index}
                  className="flex cursor-pointer items-center p-2 hover:bg-gray-100"
                  onClick={() => handleOptionClick(optionValue)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault();
                      handleOptionClick(optionValue);
                    }
                  }}
                  role="button"
                  tabIndex={0}
                >
                  <input
                    type="checkbox"
                    checked={selectedValues.includes(optionValue)}
                    onChange={() => handleOptionClick(optionValue)}
                    className="size-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label className="ml-2 w-full cursor-pointer text-gray-700">
                    {optionLabel}
                  </label>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

// Reusable InputField Component
const InputField: React.FC<InputFieldProps> = ({
  label,
  value,
  onChange,
  placeholder,
  children,
  containerClassName = '',
  labelClassName = '',
  inputClassName = '',
}) => {
  return (
    <div className={`mb-4 ${containerClassName}`}>
      <label className={`mb-2 block text-sm font-semibold text-gray-700 ${labelClassName}`}>
        {label}
        :
      </label>
      <div className="relative">
        <input
          type="text"
          className={`w-full rounded-lg border border-gray-300 p-3 text-gray-800 shadow-sm transition duration-200 ease-in-out focus:border-transparent focus:ring-2 focus:ring-blue-400 ${inputClassName}`}
          value={value}
          onChange={e => onChange(e.target.value)}
          placeholder={placeholder}
        />
        {children && <div className="absolute right-2 top-1/2 -translate-y-1/2">{children}</div>}
      </div>
    </div>
  );
};

// Simple Toast Notification Component
const Toast: React.FC<ToastProps> = ({ message, show }) => {
  if (!show) {
    return null;
  }

  return (
    <div className="animate-fade-in-out fixed bottom-5 right-5 z-50 rounded-lg bg-green-500 px-4 py-2 text-white shadow-lg">
      {message}
    </div>
  );
};

export default function GeneratePromptPage() {
  const t = useTranslations('PromptVideo');

  // Panoramic Image Analysis
  const [panoramicImage, setPanoramicImage] = useState<File | null>(null);
  const [panoramicImagePreview, setPanoramicImagePreview] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisError, setAnalysisError] = useState('');

  // Context Image Analysis
  const [isAnalyzingContext, setIsAnalyzingContext] = useState(false);
  const [contextAnalysisError, setContextAnalysisError] = useState('');

  // Section 1: Setting
  const [location, setLocation] = useState('');
  const [time, setTime] = useState('');
  const [weather, setWeather] = useState('');
  const [lightingDetails, setLightingDetails] = useState('');
  const [settingImage, setSettingImage] = useState<File | null>(null);
  const [settingImagePreview, setSettingImagePreview] = useState('');

  // Section 2: Characters
  const [characters, setCharacters] = useState<Character[]>([
    { id: 1, description: '', appearance: '', emotion: '', mainAction: '', relatedObject: '', dialogue: '', imageRef: null, imageRefPreviewUrl: '' },
  ]);

  // Section 3: Visual Style & Post-processing
  const [visualStyle, setVisualStyle] = useState<string[]>([]);
  const [lightingStyle, setLightingStyle] = useState<string[]>([]);
  const [dominantColors, setDominantColors] = useState<string[]>([]);
  const [postProcessingEffects, setPostProcessingEffects] = useState<string[]>([]);
  const [motionEffects, setMotionEffects] = useState<string[]>([]);
  const [creativeEffects, setCreativeEffects] = useState<string[]>([]);
  const [surrealElements, setSurrealElements] = useState('');

  // Section 4: Cinematography
  const [composition, setComposition] = useState<string[]>([]);
  const [cameraAngle, setCameraAngle] = useState<string[]>([]);
  const [cameraMotion, setCameraMotion] = useState<string[]>([]);
  const [focusPoint, setFocusPoint] = useState<string[]>([]);

  // Section 5: Editing & Audio
  const [editingPace, setEditingPace] = useState<string[]>([]);
  const [transitions, setTransitions] = useState<string[]>([]);
  const [mood, setMood] = useState<string[]>([]);
  const [backgroundMusic, setBackgroundMusic] = useState<string[]>([]);
  const [soundEffects, setSoundEffects] = useState<string[]>([]);
  const [typography, setTypography] = useState('');
  const [subtitleInstructions, setSubtitleInstructions] = useState<string[]>([]);

  // Section 6: Technical Specs
  const [resolution, setResolution] = useState<string[]>([]);
  const [aspectRatio, setAspectRatio] = useState<string[]>([]);
  const [duration, setDuration] = useState<string[]>([]);

  // Prompt Generation & UI State
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [toastMessage, setToastMessage] = useState('');

  // --- OPTIONS DATA ---
  const options = useMemo(() => ({
    lightingStyle: [
      { value: 'natural light', label: 'Natural Light (Ánh sáng tự nhiên)' },
      { value: 'studio lighting (key, fill, back light)', label: 'Studio Lighting (Ánh sáng studio)' },
      { value: 'high-contrast lighting (chiaroscuro)', label: 'High-Contrast (Tương phản cao)' },
      { value: 'soft light', label: 'Soft Light (Ánh sáng mềm)' },
      { value: 'hard light', label: 'Hard Light (Ánh sáng gắt)' },
      { value: 'neon glow', label: 'Neon Glow (Ánh sáng neon)' },
    ],
    visualStyle: [
      { value: 'cinematic', label: 'Cinematic (Điện ảnh)' },
      { value: 'photorealistic', label: 'Photorealistic (Chân thực)' },
      { value: 'hyper-realistic', label: 'Hyper-realistic (Siêu thực)' },
      { value: 'dreamy', label: 'Dreamy (Mộng mơ)' },
      { value: 'fantasy', label: 'Fantasy (Giả tưởng)' },
      { value: 'surreal', label: 'Surreal (Trừu tượng)' },
      { value: 'sci-fi', label: 'Sci-fi (Khoa học viễn tưởng)' },
      { value: 'futuristic', label: 'Futuristic (Tương lai)' },
      { value: 'cyberpunk', label: 'Cyberpunk' },
      { value: 'steampunk', label: 'Steampunk' },
      { value: 'vintage', label: 'Vintage (Cổ điển)' },
      { value: 'retro', label: 'Retro (Hoài cổ)' },
      { value: 'noir', label: 'Noir (Phim đen)' },
      { value: 'minimalist', label: 'Minimalist (Tối giản)' },
      { value: 'abstract', label: 'Abstract (Trừu tượng)' },
      { value: 'animated', label: 'Animated (Hoạt hình)' },
      { value: 'hand-drawn', label: 'Hand-drawn (Vẽ tay)' },
      { value: 'sketch style', label: 'Sketch (Phác họa)' },
      { value: 'watercolor', label: 'Watercolor (Màu nước)' },
      { value: 'pixel art', label: 'Pixel Art (Nghệ thuật pixel)' },
      { value: 'gritty', label: 'Gritty (Gai góc)' },
      { value: 'baroque', label: 'Baroque' },
      { value: 'impressionistic', label: 'Impressionistic (Ấn tượng)' },
    ],
    postProcessingEffects: [
      { value: 'Color Grading', label: 'Color Grading (Chỉnh màu điện ảnh)' },
      { value: 'Glow', label: 'Glow (Ánh sáng dịu mờ)' },
      { value: 'Vignette', label: 'Vignette (Tối góc)' },
      { value: 'Film Grain', label: 'Film Grain (Hạt phim)' },
      { value: 'Light Leaks', label: 'Light Leaks (Loang sáng)' },
      { value: 'Lens Flare', label: 'Lens Flare (Lóe sáng ống kính)' },
      { value: 'Sepia Tone', label: 'Sepia Tone (Tông nâu đỏ)' },
      { value: 'Monochrome', label: 'Monochrome (Đơn sắc)' },
      { value: 'Bokeh', label: 'Bokeh (Hậu cảnh mờ)' },
    ],
    motionEffects: [
      { value: 'Motion Blur', label: 'Motion Blur (Mờ chuyển động)' },
      { value: 'Speed Ramping', label: 'Speed Ramping (Thay đổi tốc độ)' },
      { value: 'Freeze Frame', label: 'Freeze Frame (Đóng băng khung hình)' },
      { value: 'Zoom / Spin / Whip Pan effects', label: 'Camera Effects (Hiệu ứng máy quay)' },
      { value: 'Tilt-Shift', label: 'Tilt-Shift (Hiệu ứng mô hình)' },
    ],
    creativeEffects: [
      { value: 'Double Exposure', label: 'Double Exposure (Chồng ảnh)' },
      { value: 'Chromatic Aberration', label: 'Chromatic Aberration (Lệch màu)' },
      { value: 'Glitch Effect', label: 'Glitch Effect (Hiệu ứng nhiễu)' },
      { value: 'Pixelate', label: 'Pixelate (Vỡ hạt)' },
      { value: 'Scan Lines', label: 'Scan Lines (Dòng quét TV)' },
    ],
    composition: [
      { value: 'rule of thirds composition', label: 'Rule of Thirds (Quy tắc 1/3)' },
      { value: 'symmetrical composition', label: 'Symmetrical (Đối xứng)' },
      { value: 'centered composition', label: 'Centered (Trung tâm)' },
      { value: 'leading lines', label: 'Leading Lines (Đường dẫn)' },
      { value: 'frame within a frame', label: 'Frame within a Frame (Khung trong khung)' },
      { value: 'unconventional framing', label: 'Unconventional Framing (Phá cách)' },
      { value: 'use of negative space', label: 'Negative Space (Không gian âm)' },
    ],
    cameraAngle: [
      { value: 'wide shot', label: 'Wide Shot (Toàn cảnh)' },
      { value: 'long shot', label: 'Long Shot (Viễn cảnh)' },
      { value: 'establishing shot', label: 'Establishing Shot (Cảnh thiết lập)' },
      { value: 'full shot', label: 'Full Shot (Toàn thân)' },
      { value: 'medium shot', label: 'Medium Shot (Trung cảnh)' },
      { value: 'two-shot', label: 'Two-shot (Cảnh hai người)' },
      { value: 'close-up', label: 'Close-up (Cận cảnh)' },
      { value: 'extreme close-up', label: 'Extreme Close-up (Siêu cận cảnh)' },
      { value: 'POV shot', label: 'POV (Góc nhìn thứ nhất)' },
      { value: 'over-the-shoulder', label: 'Over-the-shoulder (Qua vai)' },
      { value: 'low angle', label: 'Low Angle (Góc thấp)' },
      { value: 'high angle', label: 'High Angle (Góc cao)' },
      { value: 'overhead shot / top-down shot', label: 'Overhead (Từ trên xuống)' },
      { value: 'dutch angle', label: 'Dutch Angle (Góc nghiêng)' },
      { value: 'drone shot', label: 'Drone Shot (Flycam)' },
    ],
    cameraMotion: [
      { value: 'static shot (tripod)', label: 'Static (Tĩnh)' },
      { value: 'slow camera movement', label: 'Slow Movement (Chuyển động chậm)' },
      { value: 'fast camera movement', label: 'Fast Movement (Chuyển động nhanh)' },
      { value: 'smooth gimbal movement', label: 'Gimbal Movement (Chuyển động mượt)' },
      { value: 'shaky handheld camera', label: 'Handheld (Cầm tay rung)' },
      { value: 'panning', label: 'Panning (Lia ngang)' },
      { value: 'tilting', label: 'Tilting (Lia dọc)' },
      { value: 'tracking shot / following shot', label: 'Tracking (Theo dấu)' },
      { value: 'dolly zoom (Vertigo effect)', label: 'Dolly Zoom' },
      { value: 'dolly-in', label: 'Dolly-in (Tiến vào)' },
      { value: 'dolly-out', label: 'Dolly-out (Lùi ra)' },
      { value: 'crane shot', label: 'Crane (Cẩu máy)' },
      { value: 'slow motion', label: 'Slow Motion' },
      { value: 'timelapse', label: 'Timelapse (Tua nhanh)' },
      { value: 'reverse dolly', label: 'Reverse Dolly (Lùi xa)' },
    ],
    transitions: [
      { value: 'Fade In / Out', label: 'Fade In/Out (Mờ dần)' },
      { value: 'Wipe (left/right)', label: 'Wipe (Gạt cảnh)' },
      { value: 'Cross Dissolve', label: 'Cross Dissolve (Hòa tan)' },
      { value: 'Match Cut', label: 'Match Cut (Cắt nối logic)' },
      { value: 'Jump Cut', label: 'Jump Cut (Cắt giật)' },
      { value: 'Zoom Transition', label: 'Zoom Transition (Chuyển cảnh Zoom)' },
      { value: 'Whip Pan Transition', label: 'Whip Pan (Chuyển cảnh lia nhanh)' },
      { value: 'Rotate Transition', label: 'Rotate Transition (Chuyển cảnh xoay)' },
      { value: 'Hard cut', label: 'Hard Cut (Cắt thẳng)' },
    ],
    editingPace: [
      { value: 'fast-paced, energetic editing', label: 'Fast-paced (Dựng nhanh, dồn dập)' },
      { value: 'slow, smooth, and deliberate editing', label: 'Slow-paced (Dựng chậm, mượt mà)' },
    ],
    focusPoint: [
      { value: 'Main character', label: 'Main Character (Nhân vật chính)' },
      { value: 'Background', label: 'Background (Hậu cảnh)' },
      { value: 'Foreground', label: 'Foreground (Tiền cảnh)' },
      { value: 'A specific object', label: 'Specific Object (Vật thể cụ thể)' },
      { value: 'Shallow depth of field', label: 'Shallow DOF (Độ sâu trường ảnh nông)' },
      { value: 'Deep depth of field', label: 'Deep DOF (Độ sâu trường ảnh sâu)' },
    ],
    mood: [
      { value: 'mysterious', label: 'Mysterious (Huyền bí)' },
      { value: 'surreal', label: 'Surreal (Kỳ ảo)' },
      { value: 'romantic', label: 'Romantic (Lãng mạn)' },
      { value: 'dramatic', label: 'Dramatic (Kịch tính)' },
      { value: 'peaceful', label: 'Peaceful (Yên bình)' },
      { value: 'joyful', label: 'Joyful (Vui vẻ)' },
      { value: 'sad', label: 'Sad (Buồn bã)' },
      { value: 'suspenseful', label: 'Suspenseful (Hồi hộp)' },
      { value: 'adventurous', label: 'Adventurous (Phiêu lưu)' },
      { value: 'epic', label: 'Epic (Sử thi)' },
    ],
    dominantColors: [
      { value: 'warm tones', label: 'Warm Tones (Tông nóng)' },
      { value: 'cool tones', label: 'Cool Tones (Tông lạnh)' },
      { value: 'earth tones', label: 'Earth Tones (Tông màu đất)' },
      { value: 'vibrant colors', label: 'Vibrant Colors (Màu rực rỡ)' },
      { value: 'monochromatic', label: 'Monochromatic (Đơn sắc)' },
      { value: 'pastel colors', label: 'Pastel Colors (Màu pastel)' },
    ],
    backgroundMusic: [
      { value: 'gentle piano', label: 'Gentle Piano (Piano nhẹ nhàng)' },
      { value: 'mysterious electronic music', label: 'Mysterious Electronic (Nhạc điện tử huyền bí)' },
      { value: 'epic orchestral score', label: 'Epic Orchestral (Nhạc giao hưởng hoành tráng)' },
      { value: 'ambient soundscape', label: 'Ambient (Âm thanh không gian)' },
      { value: 'upbeat pop music', label: 'Upbeat Pop (Nhạc pop sôi động)' },
      { value: 'heavy rock', label: 'Heavy Rock (Rock mạnh mẽ)' },
      { value: 'no background music', label: 'No Music (Không có nhạc nền)' },
    ],
    soundEffects: [
      { value: 'wind blowing', label: 'Wind Blowing (Gió thổi)' },
      { value: 'footsteps', label: 'Footsteps (Tiếng bước chân)' },
      { value: 'city ambience', label: 'City Ambience (Không khí thành phố)' },
      { value: 'rain falling', label: 'Rain Falling (Tiếng mưa rơi)' },
      { value: 'ocean waves', label: 'Ocean Waves (Sóng biển)' },
      { value: 'explosions', label: 'Explosions (Tiếng nổ)' },
      { value: 'no sound effects', label: 'No SFX (Không có hiệu ứng âm thanh)' },
    ],
    subtitleInstructions: [
      { value: 'No subtitles', label: 'No Subtitles (Không phụ đề)' },
      { value: 'English subtitles only', label: 'English Subtitles (Chỉ phụ đề tiếng Anh)' },
      { value: 'Vietnamese subtitles only', label: 'Vietnamese Subtitles (Chỉ phụ đề tiếng Việt)' },
    ],
    resolution: [
      { value: '720p', label: '720p' },
      { value: '1080p', label: '1080p' },
      { value: '2K', label: '2K' },
      { value: '4K', label: '4K' },
      { value: '8K', label: '8K' },
    ],
    aspectRatio: [
      { value: '1:1 (square)', label: '1:1 (vuông)' },
      { value: '4:3 (classic TV)', label: '4:3 (cổ điển)' },
      { value: '16:9 (widescreen)', label: '16:9 (màn ảnh rộng)' },
      { value: '9:16 (vertical)', label: '9:16 (dọc)' },
      { value: '2.35:1 (cinemascope)', label: '2.35:1 (điện ảnh)' },
    ],
    duration: [
      { value: '5', label: '5 giây' },
      { value: '8', label: '8 giây' },
      { value: '10', label: '10 giây' },
      { value: '15', label: '15 giây' },
      { value: '20', label: '20 giây' },
      { value: '30', label: '30 giây' },
      { value: '60', label: '60 giây' },
    ],
  }), []);

  // --- HELPER FUNCTIONS ---
  const showToast = (message: string) => {
    setToastMessage(message);
    setTimeout(() => setToastMessage(''), 3000);
  };

  const copyToClipboard = (text: string, language: string) => {
    navigator.clipboard.writeText(text).then(() => {
      showToast(`Copied ${language} prompt to clipboard!`);
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  const handleAddCharacter = () => {
    setCharacters([...characters, {
      id: characters.length > 0 ? Math.max(...characters.map(c => c.id)) + 1 : 1,
      description: '',
      appearance: '',
      emotion: '',
      mainAction: '',
      relatedObject: '',
      dialogue: '',
      imageRef: null,
      imageRefPreviewUrl: '',
    }]);
  };

  const handleRemoveCharacter = (idToRemove: number) => {
    setCharacters(characters.filter(char => char.id !== idToRemove));
  };

  const handleCharacterChange = (id: number, field: keyof Omit<Character, 'id' | 'imageRef' | 'imageRefPreviewUrl'>, value: string) => {
    setCharacters(characters.map(char =>
      char.id === id ? { ...char, [field]: value } : char,
    ));
  };

  const handleSettingImageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSettingImage(file);
      setSettingImagePreview(URL.createObjectURL(file));
    } else {
      setSettingImage(null);
      setSettingImagePreview('');
    }
  };

  const analyzeContextImage = async () => {
    if (!settingImage) {
      return;
    }
    setIsAnalyzingContext(true);
    setContextAnalysisError('');
    try {
      await new Promise(resolve => setTimeout(resolve, 1500));
      setLocation('A futuristic city street at night');
      setTime('Night');
      setWeather('Light rain, neon reflections on wet ground');
      showToast('Context image analyzed and form updated!');
    } catch (err) {
      console.error(err);
      setContextAnalysisError('Failed to analyze context image.');
    } finally {
      setIsAnalyzingContext(false);
    }
  };

  const generatePromptFromForm = async () => {
    setIsLoading(true);
    setError('');
    setGeneratedPrompt('');

    try {
      // Build the prompt data
      const promptData: PromptVideoData = {
        location,
        time,
        weather,
        lightingDetails,
        characters: characters.filter(char => char.description.trim()),
        visualStyle,
        lightingStyle,
        dominantColors,
        postProcessingEffects,
        motionEffects,
        creativeEffects,
        surrealElements,
        composition,
        cameraAngle,
        cameraMotion,
        focusPoint,
        editingPace,
        transitions,
        mood,
        backgroundMusic,
        soundEffects,
        typography,
        subtitleInstructions,
        resolution,
        aspectRatio,
        duration,
      };

      // Call the API to generate video prompt and save to history
      const response = await promptVideoService.generatePromptVideo({
        prompt_data: promptData,
        model: 'gpt-4',
      });

      if (response) {
        setGeneratedPrompt(response.result);
        showToast('Video prompt generated and saved to history!');
      } else {
        throw new Error('Failed to generate prompt');
      }
    } catch (err) {
      console.error('Error generating prompt:', err);
      setError('Failed to generate prompt. Please try again.');
      toast.error('Failed to generate prompt');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="mx-auto max-w-7xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          {t('generate_title')}
        </h1>
        <p className="mt-2 text-gray-600">
          {t('page_description')}
        </p>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-sm">

        {/* Quick Image Analysis Section */}
        <div className="mb-10 rounded-lg border border-gray-300 bg-gray-100 p-6 text-center shadow-md">
          <h2 className="mb-4 flex items-center justify-center text-2xl font-bold text-green-700">
            Quick Prompt Generation from Image
          </h2>
          <div className="flex flex-col items-center justify-center gap-4">
            <input
              type="file"
              accept="image/*"
              onChange={(e) => {
                const files = e.target.files;
                const file = files ? files[0] : null;
                if (file) {
                  setPanoramicImage(file);
                  setPanoramicImagePreview(URL.createObjectURL(file));
                  setAnalysisError('');
                } else {
                  setPanoramicImage(null);
                  setPanoramicImagePreview('');
                }
              }}
              id="panoramic-upload"
              className="hidden"
            />
            <label
              htmlFor="panoramic-upload"
              className="cursor-pointer rounded-lg border border-blue-400 bg-white px-4 py-2 font-semibold text-blue-600 shadow-sm transition duration-200 hover:bg-blue-50"
            >
              Choose Image File
            </label>
            {panoramicImagePreview && (
              <div className="mt-2">
                <img src={panoramicImagePreview} alt="Preview" className="mx-auto h-auto max-h-48 max-w-xs rounded-lg object-contain shadow-sm" />
              </div>
            )}
            <button
              onClick={async () => {
                if (!panoramicImage) {
                  return;
                }

                setIsAnalyzing(true);
                setAnalysisError('');

                try {
                  // Simulate image analysis delay
                  await new Promise(resolve => setTimeout(resolve, 3000));

                  // Mock analysis results - in real implementation, this would call OpenRouter API
                  setLocation('Mountain landscape with rolling hills');
                  setTime('Golden hour sunset');
                  setWeather('Clear sky with dramatic clouds');
                  setLightingDetails('Warm golden backlight creating silhouettes');
                  setVisualStyle(['cinematic', 'photorealistic']);
                  setLightingStyle(['natural light']);
                  setCameraAngle(['wide shot', 'low angle']);
                  setMood(['peaceful', 'dramatic']);
                  setResolution(['4K']);
                  setAspectRatio(['16:9 (widescreen)']);
                  setDuration(['30']);

                  showToast('Image analyzed and form auto-filled!');
                } catch (err) {
                  console.error(err);
                  setAnalysisError('Failed to analyze image. Please try again.');
                  toast.error('Image analysis failed');
                } finally {
                  setIsAnalyzing(false);
                }
              }}
              disabled={isAnalyzing || !panoramicImage}
              className="rounded-lg bg-gradient-to-r from-green-500 to-teal-500 px-6 py-3 font-bold text-white shadow-lg transition duration-300 ease-in-out hover:scale-105 hover:from-green-600 hover:to-teal-600 focus:outline-none focus:ring-4 focus:ring-green-300 disabled:cursor-not-allowed disabled:opacity-50"
            >
              {isAnalyzing ? 'Analyzing...' : 'Analyze & Auto-fill Form'}
            </button>
          </div>
          {analysisError && <p className="mt-3 text-red-600">{analysisError}</p>}
        </div>

        {/* Section 1: Setting */}
        <div className="mb-10 rounded-lg border border-blue-200 bg-blue-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-blue-700">
            1. Setting & Environment
          </h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <InputField
              label="Location"
              value={location}
              onChange={setLocation}
              placeholder="e.g., Mountain peak, Urban street, Beach..."
            />
            <InputField
              label="Time of Day"
              value={time}
              onChange={setTime}
              placeholder="e.g., Golden hour, Night, Dawn..."
            />
            <InputField
              label="Weather"
              value={weather}
              onChange={setWeather}
              placeholder="e.g., Sunny, Rainy, Foggy..."
            />
            <InputField
              label="Lighting Details"
              value={lightingDetails}
              onChange={setLightingDetails}
              placeholder="e.g., Warm backlight, Dramatic shadows..."
            />
          </div>
          <div className="mt-4 rounded-md border border-blue-200 bg-blue-100 p-3">
            <label htmlFor="context-image-upload" className="mb-2 block text-sm font-semibold text-gray-700">
              Upload Context Image (optional):
            </label>
            <div className="flex items-center gap-4">
              <input
                id="context-image-upload"
                type="file"
                accept="image/*"
                onChange={handleSettingImageChange}
                className="block w-full text-sm text-gray-500 file:mr-4 file:rounded-full file:border-0 file:bg-white file:py-2 file:px-4 file:text-sm file:font-semibold file:text-blue-700 hover:file:bg-blue-50"
              />
              <button
                onClick={analyzeContextImage}
                disabled={isAnalyzingContext || !settingImage}
                className="whitespace-nowrap rounded-lg bg-blue-500 px-4 py-2 text-white shadow-md transition hover:bg-blue-600 disabled:cursor-not-allowed disabled:opacity-50"
              >
                {isAnalyzingContext ? 'Analyzing...' : 'Analyze'}
              </button>
            </div>
            {contextAnalysisError && <p className="mt-2 text-sm text-red-600">{contextAnalysisError}</p>}
            {settingImagePreview && (
              <div className="mt-2 text-center">
                <img src={settingImagePreview} alt="Setting Preview" className="mx-auto h-40 max-w-full rounded-lg object-contain shadow-sm" />
              </div>
            )}
          </div>
        </div>

        {/* Section 2: Characters */}
        <div className="mb-10 rounded-lg border border-green-200 bg-green-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-green-700">
            2. Characters
          </h2>
          {characters.map((character, index) => (
            <div key={character.id} className="mb-6 rounded-lg border border-gray-200 bg-white p-4">
              <div className="mb-3 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-700">
                  Character
                  {' '}
                  {index + 1}
                </h3>
                {characters.length > 1 && (
                  <button
                    onClick={() => handleRemoveCharacter(character.id)}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Remove
                  </button>
                )}
              </div>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                <InputField
                  label="Description"
                  value={character.description}
                  onChange={(value: string) => handleCharacterChange(character.id, 'description', value)}
                  placeholder="e.g., Young woman, elderly man..."
                />
                <InputField
                  label="Appearance"
                  value={character.appearance}
                  onChange={(value: string) => handleCharacterChange(character.id, 'appearance', value)}
                  placeholder="e.g., Red dress, casual clothes..."
                />
                <InputField
                  label="Emotion"
                  value={character.emotion}
                  onChange={(value: string) => handleCharacterChange(character.id, 'emotion', value)}
                  placeholder="e.g., Happy, contemplative..."
                />
                <InputField
                  label="Main Action"
                  value={character.mainAction}
                  onChange={(value: string) => handleCharacterChange(character.id, 'mainAction', value)}
                  placeholder="e.g., Walking, dancing..."
                />
                <InputField
                  label="Related Object"
                  value={character.relatedObject}
                  onChange={(value: string) => handleCharacterChange(character.id, 'relatedObject', value)}
                  placeholder="e.g., Holding a lantern..."
                />
                <InputField
                  label="Dialogue"
                  value={character.dialogue}
                  onChange={(value: string) => handleCharacterChange(character.id, 'dialogue', value)}
                  placeholder="e.g., 'The world is magical.'"
                />
              </div>
            </div>
          ))}
          <button
            onClick={handleAddCharacter}
            className="rounded-lg bg-green-600 px-4 py-2 text-white transition-colors hover:bg-green-700"
          >
            Add Character
          </button>
        </div>

        {/* Section 3: Visual Style & Post-processing */}
        <div className="mb-10 rounded-lg border border-purple-200 bg-purple-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-purple-700">
            3. Visual Style & Post-processing
          </h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <MultiSelectDropdown
              label="Visual Style"
              options={options.visualStyle}
              selectedValues={visualStyle}
              onChange={setVisualStyle}
              placeholder="Select visual styles..."
            />
            <MultiSelectDropdown
              label="Lighting Style"
              options={options.lightingStyle}
              selectedValues={lightingStyle}
              onChange={setLightingStyle}
              placeholder="Select lighting styles..."
            />
            <MultiSelectDropdown
              label="Dominant Colors"
              options={options.dominantColors}
              selectedValues={dominantColors}
              onChange={setDominantColors}
              placeholder="Select dominant colors..."
            />
            <MultiSelectDropdown
              label="Post-processing Effects"
              options={options.postProcessingEffects}
              selectedValues={postProcessingEffects}
              onChange={setPostProcessingEffects}
              placeholder="Select post-processing effects..."
            />
            <MultiSelectDropdown
              label="Motion Effects"
              options={options.motionEffects}
              selectedValues={motionEffects}
              onChange={setMotionEffects}
              placeholder="Select motion effects..."
            />
            <MultiSelectDropdown
              label="Creative Effects"
              options={options.creativeEffects}
              selectedValues={creativeEffects}
              onChange={setCreativeEffects}
              placeholder="Select creative effects..."
            />
          </div>
          <div className="mt-6">
            <InputField
              label="Surreal Elements"
              value={surrealElements}
              onChange={setSurrealElements}
              placeholder="e.g., Floating whales, glowing trees..."
            />
          </div>
        </div>

        {/* Section 4: Cinematography */}
        <div className="mb-10 rounded-lg border border-red-200 bg-red-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-red-700">
            4. Cinematography
          </h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <MultiSelectDropdown
              label="Composition"
              options={options.composition}
              selectedValues={composition}
              onChange={setComposition}
              placeholder="Select composition..."
            />
            <MultiSelectDropdown
              label="Camera Angle"
              options={options.cameraAngle}
              selectedValues={cameraAngle}
              onChange={setCameraAngle}
              placeholder="Select camera angles..."
            />
            <MultiSelectDropdown
              label="Camera Motion"
              options={options.cameraMotion}
              selectedValues={cameraMotion}
              onChange={setCameraMotion}
              placeholder="Select camera motion..."
            />
            <MultiSelectDropdown
              label="Focus Point"
              options={options.focusPoint}
              selectedValues={focusPoint}
              onChange={setFocusPoint}
              placeholder="Select focus point..."
            />
          </div>
        </div>

        {/* Section 5: Editing & Audio */}
        <div className="mb-10 rounded-lg border border-teal-200 bg-teal-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-teal-700">
            5. Editing & Audio
          </h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <MultiSelectDropdown
              label="Editing Pace"
              options={options.editingPace}
              selectedValues={editingPace}
              onChange={setEditingPace}
              placeholder="Select editing pace..."
            />
            <MultiSelectDropdown
              label="Transitions"
              options={options.transitions}
              selectedValues={transitions}
              onChange={setTransitions}
              placeholder="Select transitions..."
            />
            <MultiSelectDropdown
              label="Mood"
              options={options.mood}
              selectedValues={mood}
              onChange={setMood}
              placeholder="Select mood..."
            />
            <MultiSelectDropdown
              label="Background Music"
              options={options.backgroundMusic}
              selectedValues={backgroundMusic}
              onChange={setBackgroundMusic}
              placeholder="Select background music..."
            />
            <MultiSelectDropdown
              label="Sound Effects"
              options={options.soundEffects}
              selectedValues={soundEffects}
              onChange={setSoundEffects}
              placeholder="Select sound effects..."
            />
            <MultiSelectDropdown
              label="Subtitle Instructions"
              options={options.subtitleInstructions}
              selectedValues={subtitleInstructions}
              onChange={setSubtitleInstructions}
              placeholder="Select subtitle instructions..."
            />
          </div>
          <div className="mt-6">
            <InputField
              label="Typography"
              value={typography}
              onChange={setTypography}
              placeholder="e.g., Serif font, fading in and out..."
            />
          </div>
        </div>

        {/* Section 6: Technical Specifications */}
        <div className="mb-10 rounded-lg border border-orange-200 bg-orange-50 p-6">
          <h2 className="mb-4 text-2xl font-bold text-orange-700">
            6. Technical Specifications
          </h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <MultiSelectDropdown
              label="Resolution"
              options={options.resolution}
              selectedValues={resolution}
              onChange={setResolution}
              placeholder="Select resolution..."
            />
            <MultiSelectDropdown
              label="Aspect Ratio"
              options={options.aspectRatio}
              selectedValues={aspectRatio}
              onChange={setAspectRatio}
              placeholder="Select aspect ratio..."
            />
            <MultiSelectDropdown
              label="Duration (seconds)"
              options={options.duration}
              selectedValues={duration}
              onChange={setDuration}
              placeholder="Select duration..."
            />
          </div>
        </div>

        {/* Generate Button */}
        <div className="mb-8 text-center">
          <button
            onClick={generatePromptFromForm}
            disabled={isLoading}
            className="rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 px-8 py-4 text-lg font-bold text-white shadow-lg transition duration-300 ease-in-out hover:scale-105 hover:from-blue-700 hover:to-purple-700 focus:outline-none focus:ring-4 focus:ring-blue-300 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading
              ? (
                  <div className="flex items-center justify-center">
                    <svg className="-ml-1 mr-3 size-5 animate-spin text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Generating...
                  </div>
                )
              : 'Generate Video Prompt'}
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 rounded border border-red-400 bg-red-100 px-4 py-3 text-red-700">
            {error}
          </div>
        )}

        {/* Generated Prompt Display */}
        {generatedPrompt && (
          <div className="mb-8 rounded-lg border border-gray-200 bg-gray-50 p-6">
            <div className="mb-3 flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-800">Generated Video Prompt</h3>
              <button
                onClick={() => copyToClipboard(generatedPrompt, 'English')}
                className="font-medium text-blue-600 hover:text-blue-800"
              >
                Copy Prompt
              </button>
            </div>
            <div className="whitespace-pre-wrap rounded border bg-white p-4 text-gray-700">
              {generatedPrompt}
            </div>
          </div>
        )}

        <Toast message={toastMessage} show={!!toastMessage} />
      </div>
    </div>
  );
}
