'use client';

import { useTranslations } from 'next-intl';
import React, { useEffect, useRef } from 'react';

// Default Discord invite link if API fails
const DISCORD_INVITE_LINK = 'https://discord.gg/ceNCcvGg8w';
// Default ticket creation link
const TICKET_CREATION_LINK = 'https://discord.gg/ceNCcvGg8w';

type TryForFreeModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

const TryForFreeModal: React.FC<TryForFreeModalProps> = ({ isOpen, onClose }) => {
  const t = useTranslations('TryForFreeModal');
  const modalRef = useRef<HTMLDivElement>(null);

  // Close modal when clicking outside
  useEffect(() => {
    if (!isOpen) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen, onClose]);

  // Disable body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  if (!isOpen) {
    return null;
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
      <div
        ref={modalRef}
        className="size-full max-w-3xl overflow-y-auto rounded-xl bg-white shadow-2xl transition-all duration-300 ease-in-out md:h-auto md:overflow-hidden"
        style={{ animation: 'fadeIn 0.3s ease-in-out' }}
      >
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-700 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <h3 className="text-2xl font-bold">{t('title')}</h3>
            <button
              type="button"
              onClick={onClose}
              className="rounded-full p-1 text-white transition-colors hover:bg-white/20"
            >
              <svg
                className="size-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Modal Body */}
        <div className="p-6">
          <h4 className="mb-6 text-center text-xl font-semibold">{t('follow_steps')}</h4>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2">
            {/* Step 1 */}
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
              <div className="mb-4 flex items-center">
                <div className="flex size-10 items-center justify-center rounded-full bg-blue-600 text-white">
                  1
                </div>
                <h5 className="ml-3 text-lg font-semibold">{t('step1_title')}</h5>
              </div>

              <div className="mb-4 flex justify-center">
                <div className="flex size-16 items-center justify-center rounded-xl bg-indigo-600 text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z" />
                  </svg>
                </div>
              </div>

              <p className="mb-4 text-gray-600">
                {t('step1_description')}
              </p>

              <p className="mb-4 text-sm italic text-gray-500">
                {t('step1_note')}
              </p>

              <div className="text-center">
                <a
                  href={DISCORD_INVITE_LINK}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block rounded-lg bg-indigo-600 px-5 py-2 text-center font-medium text-white shadow-md transition-all hover:bg-indigo-700"
                >
                  {t('step1_button')}
                </a>
              </div>
            </div>

            {/* Step 2 */}
            <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-md">
              <div className="mb-4 flex items-center">
                <div className="flex size-10 items-center justify-center rounded-full bg-green-600 text-white">
                  2
                </div>
                <h5 className="ml-3 text-lg font-semibold">{t('step2_title')}</h5>
              </div>

              <div className="mb-4 flex justify-center">
                <div className="flex size-16 items-center justify-center rounded-xl bg-green-600 text-white">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="32"
                    height="32"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                    <polyline points="14 2 14 8 20 8" />
                    <line x1="16" y1="13" x2="8" y2="13" />
                    <line x1="16" y1="17" x2="8" y2="17" />
                    <line x1="10" y1="9" x2="8" y2="9" />
                  </svg>
                </div>
              </div>

              <p className="mb-6 text-gray-600">
                {t('step2_description')}
              </p>

              <div className="text-center">
                <a
                  href={TICKET_CREATION_LINK}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block rounded-lg bg-green-600 px-5 py-2 text-center font-medium text-white shadow-md transition-all hover:bg-green-700"
                >
                  {t('step2_button')}
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="flex justify-end">
            <button
              type="button"
              onClick={onClose}
              className="rounded-lg bg-gray-400 px-4 py-2 text-sm font-medium text-white shadow-sm transition-all hover:bg-blue-700"
            >
              {t('close')}
            </button>
          </div>
        </div>
      </div>

      {/* Global styles for animation */}
      <style jsx global>
        {`
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: scale(0.95);
            }
            to {
              opacity: 1;
              transform: scale(1);
            }
          }
        `}
      </style>
    </div>
  );
};

export default TryForFreeModal;
