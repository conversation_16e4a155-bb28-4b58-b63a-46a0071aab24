import React from 'react';

const PricingTable: React.FC = () => {
  const tools = [
    { name: 'Midjourney', originPrice: '$30', price: '$1' },
    { name: 'Leonardo AI', originPrice: '$30', price: '$1' },
    { name: '<PERSON><PERSON> ML', originPrice: '$95', price: '$1' },
    { name: 'Elevenlabs AI', originPrice: '$25', price: '$1' },
    { name: 'Hailuo AI', originPrice: '$95', price: '$1' },
    { name: 'Kling AI', originPrice: '$90', price: '$1' },
    { name: 'Heygen', originPrice: '$29', price: '$1' },
    { name: 'ChatGPT', originPrice: '$20', price: '$1' },
    { name: '<PERSON><PERSON>', originPrice: '$30', price: '$1' },
    { name: '<PERSON>', originPrice: '$175', price: '$1' },
    { name: '<PERSON>', originPrice: '$20', price: '$1' },
    { name: '<PERSON><PERSON>', originPrice: '$65', price: '$1' },
    { name: 'Capcut Pro', originPrice: '$39', price: '$1' },
    { name: 'Monica', originPrice: '$199', price: '$1' },
    { name: 'Shophunter', originPrice: '$500', price: '$1' },
    { name: 'Dropship.io', originPrice: '$99', price: '$1' },
    { name: 'Winning Hunter ', originPrice: '$49', price: '$1' },
    { name: 'Vidiq', originPrice: '$415', price: '$1' },
    { name: 'Dzine', originPrice: '$50', price: '$1' },
    { name: 'Shoplus', originPrice: '$99', price: '$1' },
    { name: 'Suno AI', originPrice: '$10', price: '$1' },
    { name: 'Helium 10', originPrice: '$249', price: '$1' },
    { name: 'Freepik', originPrice: '$175', price: '$1' },
    { name: 'Vidu AI', originPrice: '$49', price: '$1' },
    {
      name: '',
      originPrice: '≈$3000',
      price: '$ <25',
    },
  ];

  return (
    <div className="grid grid-cols-1">
      <div className="w-full">
        <div className="mx-auto mt-7 max-w-3xl overflow-hidden rounded-lg shadow-lg">
          <div className="grid grid-cols-4">
            {/* Column Headers */}
            <div className="col-span-2 rounded-tl-lg border-t"></div>
            <div className="border-t py-4 text-center">
              <span className="text-lg font-bold text-gray-800">Regular Price</span>
            </div>
            <div className="rounded-t-lg border-2 border-green-500 py-4 text-center">
              <span className="text-lg font-bold text-gray-800">Kitsify Tools</span>
            </div>

            {/* Tool Rows */}
            {tools.map((tool, i) => (
              <React.Fragment key={`tool-${i}-${tool.name}`}>
                <div className="col-span-2 border-b border-gray-200 bg-gray-50 px-6 py-4">
                  <p className="font-medium text-gray-800">
                    {tool.name}
                  </p>
                </div>
                <div className="flex items-center justify-center border-b border-gray-200 bg-gray-50 px-6 py-3">
                  <span className="font-medium text-gray-700">{tool.originPrice}</span>
                </div>
                <div className={`flex items-center justify-center border-x-2 border-b-2 border-green-500 px-6 py-3 ${i < tools.length - 1 ? '' : 'rounded-b-lg'}`}>
                  <div className="flex items-center">
                    <span className="mr-2 font-medium text-emerald-600">{tool.price}</span>
                  </div>
                </div>
              </React.Fragment>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingTable;
